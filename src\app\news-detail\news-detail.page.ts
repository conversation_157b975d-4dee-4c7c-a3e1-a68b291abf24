import { Component, OnInit } from '@angular/core';
import { GenericService } from '../generic.service';

import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { Storage } from '@ionic/storage';
import { Platform } from '@ionic/angular';
import { ActivatedRoute } from '@angular/router';
import { ToastService } from '../service/toast.service';
import { HttpService } from '../service/http.service';

@Component({
	selector: 'app-news-detail',
	templateUrl: './news-detail.page.html',
	styleUrls: ['./news-detail.page.scss'],
})
export class NewsDetailPage implements OnInit {

	newsinfo: any;
	detailid:any;
	showLoader:any =true
  	showMessage:any = false
	
	constructor(private activatedRoute: ActivatedRoute, 
		public platform: Platform, 
		public generic: GenericService, 
		private storage: Storage, 
		private httpSerice: HttpService,
		public toaster: ToastService,
		){
		// generic.setup();
		this.detailid = this.activatedRoute.snapshot.paramMap.get('id');
	}
	

	ngOnInit() {
		this.getNewsDetails()
	}

	getNewsDetails(){
		this.httpSerice.getDetails('news-detail',this.generic.appid, this.detailid).subscribe((response:any) => {
			if(response.status == 200){
				if(response.data ){
					this.newsinfo = response.data;
					this.showLoader = false
				} else {
					this.showLoader = false
					this.showMessage = true

				}
			} else {
				this.showMessage = true
				this.showLoader = false
			}
		}, err=>{
			this.showMessage = true
        	this.toaster.showToaster("Something went wrong!", 'danger')
        	this.showLoader = false
		});

	}

}
