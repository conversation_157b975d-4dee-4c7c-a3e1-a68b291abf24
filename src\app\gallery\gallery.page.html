<app-header></app-header>

<ion-content class="ion-content-height">

<div class="feed_header">
  <div (click)="generic.loadTile('home','#')">
    <div class="back" [ngStyle]="{ 'border-right' : '10px solid ' + generic.secondColor }"></div>
  </div>
  <div>Gallery</div>
  <div>&nbsp;</div>
</div>

<div class="galleryContainer">
  <div class="gallery" (click)="generic?.loadTile('gallery-detail','#', gallery?.id)" *ngFor="let gallery of galleries; let i=index;">

    <div>
      <div class="galimage" [ngStyle]="{'background-image': 'url(' + galleryImage(gallery) + ')'}"></div>
    </div>

    <!-- <div *ngIf="generic.appinfo.website_type ==  'wp'">
      <div class="galimage" [ngStyle]="{'background-image': 'url(' + gallery?.dfile + ')'}"></div>
    </div>
    <div *ngIf="generic.appinfo.website_type ==  'php'">
      <div class="galimage" [ngStyle]="{'background-image': 'url(' + generic.appinfo.url + 'images/image_gallery/'+ gallery?.dfile + ')'}"></div>
    </div> -->

    <h2 class="galleryTitle">{{ gallery?.name  }}</h2>
  </div>
</div>
<div class="no-data-style" *ngIf="showMessage">
  <p>No data found!</p>
</div>
<div *ngIf="showLoader" class="loader-style">
  <ion-spinner name="crescent" style="color: #158145;"></ion-spinner>
</div>
</ion-content>