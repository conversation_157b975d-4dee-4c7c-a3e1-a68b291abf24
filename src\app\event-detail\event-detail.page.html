<app-header></app-header>

<ion-content class=" ion-content-height">

<div class="feed_header">
  <div (click)="generic.loadTile('events','#')">
      <div class="back" [ngStyle]="{ 'border-right' : '10px solid ' + generic.secondColor }"></div>
  </div>
  <div>Events</div>
  <div>&nbsp;</div>
</div>

<div class="eventdetail">
    <div class="bigDate" [ngStyle]="{ 'background-color' : generic.firstColor}">
        <div class="iconbg">
          <span>{{ eventinfo?.dateD }}</span>
          <span>{{ eventinfo?.dateM }}</span>
         </div>
    </div>

    <h2 class="eventdetail" [ngStyle]="{ 'color' : generic.firstColor}">{{ eventinfo?.page_name }}</h2>
    <p class="eventdetail">
      {{ eventinfo?.lead_in_description }}
    </p>
    
    <div class="addcal" (click)="addToCalendar()" [ngStyle]="{ 'background-color' : generic.firstColor}">Add to calendar</div>
    <div *ngIf="showLoader" class="loader-style">
      <ion-spinner name="crescent" style="color: #158145;"></ion-spinner>
    </div>
</div>
</ion-content>
