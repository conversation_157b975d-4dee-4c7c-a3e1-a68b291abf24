import { Injectable } from '@angular/core';
import { environment } from 'src/environments/environment';
import { HttpClient } from '@angular/common/http';


@Injectable({
  providedIn: 'root'
})
export class HttpService {

  constructor(public httpService: HttpClient) { }

  
  getAllRecords(val, type){
    return this.httpService.get(`${environment.baseUrl}/?dev=1&key=trickydicky&feed=${type}&school_id=${val}`)	
  }

  getDetails(type, appId, detailId){
    return this.httpService.get(`${environment.baseUrl}/?dev=1&key=trickydicky&feed=${type}&school_id=${appId}&detailid=${detailId}`)
  }


}
