import { Component, OnInit } from '@angular/core';
import { GenericService } from '../generic.service';

import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { Storage } from '@ionic/storage';
import * as moment from 'moment';
import { ActivatedRoute } from '@angular/router';
import { Calendar } from '@ionic-native/calendar/ngx';
import { environment } from 'src/environments/environment';
import { HttpService } from '../service/http.service';
import { ToastService } from '../service/toast.service';


@Component({
  selector: 'app-event-detail',
  templateUrl: './event-detail.page.html',
  styleUrls: ['./event-detail.page.scss'],
})

export class EventDetailPage implements OnInit {

  eventinfo: any;
  detailId: any;
  showLoader: any = true

  constructor(
    private activatedRoute: ActivatedRoute,
    public generic: GenericService,
    private storage: Storage,
    private httpService: HttpService,
    private toaster: ToastService,
    private calendar: Calendar
  ) {
    // generic.setup();
    this.detailId = this.activatedRoute.snapshot.paramMap.get('id');
  }
  ngOnInit() {
    this.getEventDetails()
  }

  getEventDetails() {
    this.httpService.getDetails('event-detail', this.generic.appid, this.detailId).subscribe((response: any) => {
      if (response.status == 200) {
        if (response.data) {
          this.eventinfo = response.data;
          this.showLoader = false
        } else {
          this.showLoader = false
        }
      } else {
        this.showLoader = false
      }
    }, err => {
      this.toaster.showToaster("Something went wrong!", 'danger')
      this.showLoader = false
    })
  }

  addToCalendar() {
    var startDate:any;
    var endDate:any;
    if(this.eventinfo.timestart && this.eventinfo.timeend){
      let mergeStartDate = moment(this.eventinfo.posted_date + " " + this.eventinfo.timestart).format();
      let mergeEndDate = moment(this.eventinfo.posted_date + " " + this.eventinfo.timeend).format();
      startDate = new Date(mergeStartDate);
      endDate = new Date(mergeEndDate);
    }else {
      startDate = new Date(this.eventinfo.y, this.eventinfo.m2, this.eventinfo.d, 8, 0, 0, 0);
      endDate = new Date(this.eventinfo.y, this.eventinfo.m2, this.eventinfo.d, 16, 0, 0, 0);
    }

    this.calendar.createEventInteractively(this.eventinfo.page_name, null, null, startDate, endDate);
  }


}