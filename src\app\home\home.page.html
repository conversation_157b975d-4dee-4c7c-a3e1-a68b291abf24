<app-header></app-header>

<ion-content class=" ion-content-height">
  <ion-refresher (ionRefresh)="doRefresh($event)" style="padding-top:50px;padding-bottom:50px;">
    <ion-refresher-content
      pullingIcon="arrow-dropdown"
      pullingText=" "
      refreshingSpinner="circles"
      refreshingText=" ">
    </ion-refresher-content>
    <br/><br/>
  </ion-refresher>

  <div class="tiles">
    <div class="tile" (click)="generic?.loadTile(tile?.template, tile.link)" *ngFor="let tile of tiles; let i=index;">
      <div class="icon" [ngStyle]="{ 'background-color' : generic?.firstColor}" *ngIf="tile.row">
        <img [src]=" tile?.icon ">

      </div>

      <div class="icon" [ngStyle]="{ 'background-color' : generic?.secondColor}" *ngIf="tile.row == false">
          <img [src]=" tile.icon ">
      </div> 

      <div class="content"> 
        <span class="circleNotification" *ngIf="tile?.template =='newsletters' && generic?.unreadNewslettersCount > 0"> {{ generic?.unreadNewslettersCount }} </span>
        <span class="circleNotification" *ngIf="tile?.template =='events' && generic?.unreadEventsCount > 0"> {{ generic?.unreadEventsCount }} </span>
        <span class="circleNotification" *ngIf="tile?.template =='news' && generic?.unreadNewsCount > 0"> {{ generic?.unreadNewsCount }} </span>
        <span class="circleNotification" *ngIf="tile?.template =='letters' && generic?.unreadLettersCount > 0"> {{ generic?.unreadLettersCount }} </span>
        <div style="width:70%;">{{ tile?.name }}</div>
        <div class="arrow">
          <img src="/assets/arrowicon.png" height="20">
        </div>
      </div>
    </div>
  </div>
  <div *ngIf="showLoader" class="loader-style">
    <ion-spinner name="crescent" style="color: #158145;"></ion-spinner>
  </div>
</ion-content>


<script>
  $(document).ready(function(){
    $('.pulltab').click(function(){
      $('.footertabContent').css('display','block');
    });
  });
</script>