{"$schema": "./node_modules/@angular-devkit/core/src/workspace/workspace-schema.json", "version": 1, "defaultProject": "app", "newProjectRoot": "projects", "projects": {"app": {"root": "", "sourceRoot": "src", "projectType": "application", "prefix": "app", "schematics": {}, "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "www", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "src/tsconfig.app.json", "assets": [{"glob": "**/*", "input": "src/assets", "output": "assets"}, {"glob": "**/*.svg", "input": "node_modules/ionicons/dist/ionicons/svg", "output": "./svg"}], "styles": [{"input": "src/theme/variables.scss"}, {"input": "src/global.scss"}], "scripts": [], "es5BrowserSupport": true}, "configurations": {"production": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "extractCss": true, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}]}, "ci": {"progress": false}}}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "app:build"}, "configurations": {"production": {"browserTarget": "app:build:production"}, "ci": {"progress": false}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "app:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "src/tsconfig.spec.json", "karmaConfig": "src/karma.conf.js", "styles": [], "scripts": [], "assets": [{"glob": "favicon.ico", "input": "src/", "output": "/"}, {"glob": "**/*", "input": "src/assets", "output": "/assets"}]}, "configurations": {"ci": {"progress": false, "watch": false}}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["src/tsconfig.app.json", "src/tsconfig.spec.json"], "exclude": ["**/node_modules/**"]}}, "ionic-cordova-build": {"builder": "@ionic/angular-toolkit:cordova-build", "options": {"browserTarget": "app:build"}, "configurations": {"production": {"browserTarget": "app:build:production"}}}, "ionic-cordova-serve": {"builder": "@ionic/angular-toolkit:cordova-serve", "options": {"cordovaBuildTarget": "app:ionic-cordova-build", "devServerTarget": "app:serve"}, "configurations": {"production": {"cordovaBuildTarget": "app:ionic-cordova-build:production", "devServerTarget": "app:serve:production"}}}}}, "app-e2e": {"root": "e2e/", "projectType": "application", "architect": {"e2e": {"builder": "@angular-devkit/build-angular:protractor", "options": {"protractorConfig": "e2e/protractor.conf.js", "devServerTarget": "app:serve"}, "configurations": {"ci": {"devServerTarget": "app:serve:ci"}}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": "e2e/tsconfig.e2e.json", "exclude": ["**/node_modules/**"]}}}}}, "cli": {"defaultCollection": "@ionic/angular-toolkit", "analytics": false}, "schematics": {"@ionic/angular-toolkit:component": {"styleext": "scss"}, "@ionic/angular-toolkit:page": {"styleext": "scss"}}}