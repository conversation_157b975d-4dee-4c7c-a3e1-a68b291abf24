import { Component, OnInit } from '@angular/core';
import { GenericService } from '../generic.service';

import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { Storage } from '@ionic/storage';
import { ToastService } from '../service/toast.service';
import { HttpService } from '../service/http.service';
@Component({
  selector: 'app-event-list',
  templateUrl: './event-list.page.html',
  styleUrls: ['./event-list.page.scss'],
})
export class EventListPage implements OnInit {

  events: any;
  showLoader:any =true
  showMessage:any = false

  constructor(public generic: GenericService, 
    private storage: Storage, 
    private httpService: HttpService, 
    public toaster: ToastService) 
  {
    // generic.setup();
    //alert(generic.unreadNewslettersIDS);
  }

  ngOnInit() {
    this.getAllCalendorEvent()
  }

  getAllCalendorEvent(){
    this.storage.get('schoolid').then((val) => {
      this.httpService.getAllRecords(val, 'event').subscribe((response:any) => {
        if(response.status == 200){
          if(response.data && response.data.length > 0){
            this.events = response.data;
            this.showLoader = false
          } else {
            this.showLoader = false
            this.showMessage = true
          }
        } else {
          this.showLoader = false
          this.showMessage = true
        }
      }, err=>{
        this.showMessage = true
        this.toaster.showToaster("Something went wrong!", 'danger')
        this.showLoader = false
      });
    });
  }

  swipeEvent() {
    //if (e.direction == 3) {
        this.generic.loadTile("home", '#')
   // }
}
  


}
