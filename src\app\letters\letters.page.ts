import { Component, OnInit } from '@angular/core';
import { GenericService } from '../generic.service';

import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { Storage } from '@ionic/storage';
import { ToastService } from '../service/toast.service';
import { HttpService } from '../service/http.service';

@Component({
  selector: 'app-letters',
  templateUrl: './letters.page.html',
  styleUrls: ['./letters.page.scss'],
})
export class LettersPage implements OnInit {

  letters: any;
  showLoader:any =true
  showMessage:any = false

  constructor(private storage: Storage, 
    public generic: GenericService, 
    private httpService: HttpService,
    public toaster: ToastService
    ){
      // generic.setup();
    }
  
    ngOnInit() {
      this.getAllLetters()
    }

    getAllLetters(){
      this.storage.get('schoolid').then((val) => {
        this.httpService.getAllRecords(val, 'letters').subscribe((response:any) => {
          if(response.status == 200){
            if(response.data && response.data.length > 0){
              this.letters = response.data;
              this.showLoader = false
            } else {
              this.showLoader = false
              this.showMessage = true
            }
          } else {
            this.showLoader = false
            this.showMessage = true
          }
        },err=>{
          this.showMessage = true
          this.toaster.showToaster("Something went wrong!", 'danger')
          this.showLoader = false
        });
      });
    }
  
  }
  