<div class="ion-content-height">
  <button class="close" (click)="onClose()">
    <ion-icon name="close-outline"></ion-icon>
  </button>

  <ion-slides #slider pager="false">
    <ion-slide *ngFor="let gallery of galleryImages; let i=index;">
      <div class="feed" *ngIf="generic.appinfo.website_type ==  'wp'">
        <img
          [src]="gallery.dfile"
          [alt]="gallery.dfile"
        />
      </div>
      <div class="feed" *ngIf="generic.appinfo.website_type ==  'php'">
        <img
          [src]="generic.appinfo.url + 'images/image_gallery/large/'+ gallery.dfile"
          [alt]="gallery.dfile"
        />
      </div>
    </ion-slide>
  </ion-slides>

  <!-- <button class="button left" (click)="previousImage()" *ngIf="imageIndex !== 0">
    <ion-icon name="chevron-forward-outline"></ion-icon>
  </button>
  <button class="button right" (click)="nextImage()" *ngIf="imageIndex < galleryImages.length - 1">
    <ion-icon name="chevron-forward-outline"></ion-icon>
  </button> -->

</div>
