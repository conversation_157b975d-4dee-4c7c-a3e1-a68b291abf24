import { NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { RouteReuseStrategy } from '@angular/router';

import { IonicModule, IonicRouteStrategy } from '@ionic/angular';
import { SplashScreen } from '@ionic-native/splash-screen/ngx';
import { StatusBar } from '@ionic-native/status-bar/ngx'; 

import { AppComponent } from './app.component';
import { AppRoutingModule } from './app-routing.module'; 

import { HttpClientModule } from '@angular/common/http';

import { IonicStorageModule } from '@ionic/storage';

import { Calendar } from '@ionic-native/calendar/ngx';
import { PhotoViewer } from '@ionic-native/photo-viewer/ngx';
import { OneSignal } from '@ionic-native/onesignal/ngx';
import { CallNumber } from '@ionic-native/call-number/ngx';
import { InAppBrowser } from '@ionic-native/in-app-browser/ngx';
import { ScreenOrientation } from '@ionic-native/screen-orientation/ngx';
import { GalleryModal } from './shared/galleryModal/galleryModal.page';


@NgModule({
  declarations: [AppComponent, GalleryModal],
  entryComponents: [],
  imports: [
    BrowserModule,
    HttpClientModule,
    IonicModule.forRoot({
      scrollAssist: false,
    }),
    AppRoutingModule,
    IonicStorageModule.forRoot(),
  ],
  providers: [
    StatusBar,
    SplashScreen,
    { provide: RouteReuseStrategy, useClass: IonicRouteStrategy },
    Calendar,
    PhotoViewer,
    OneSignal,
    CallNumber,
    InAppBrowser,
    ScreenOrientation
  ],
  bootstrap: [AppComponent]
})
export class AppModule {}
