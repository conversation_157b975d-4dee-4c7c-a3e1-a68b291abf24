/************/
header{
  width:100%;
  height:250px;
  background: url("../../assets/phenix_splatter.png") no-repeat, #0a465f;
  background-size: cover;
  background-position: center center;
  position: relative;

}


.homeIcon{
  position: absolute;
  top: 45px;
  left: 20px;
  width: 30px;
}

.settingsIcon{
  position: absolute;
  top: 45px;
  right: 20px;
  width: 30px;
}


.schoolLogo{
  width: 105px;
  height: 105px;
  border-radius: 100%;
  margin: 0 auto;
  display: block;
  position: absolute;
  left:50%;
  top:50%;
  transform: translateY(-50%) translateX(-50%);
  background-color: white;
  padding: 5px;
}

.schoolText{
  position: absolute;
  bottom:20px;
  width:100%;
  left:50%;
  transform:translateX(-50%);
}

h2.schoolName{
  text-align: center;
  font-size: 78%;
  color: #fff;
  font-weight: 700;
  padding-top: 24px;
  margin:0;
  text-transform: uppercase;
}

h2.motto{
  text-align: center;
  font-size: 50%;
  color: #fff;
  margin: 0px;
  padding: 0px;
  padding-top:7px;
}
/************/


div.feed_header{
	text-align:center;
	background:#eeeeee;
	color:#0a465f;
	text-transform: uppercase;
	font-weight:700;
	padding-top:10px;
	padding-bottom:10px;
	font-size:80%;
	display:flex;
	justify-content: space-evenly;
}

div.feed{ 
}

div.feed .item{
	width:100%;
	display:flex;
  border-bottom:1px solid #aaaaaa;
}

.item .date{
	width: 75px;
	height: 76px;
  text-align:center;
}

.item .iconbg{
  background-image: url(../../assets/eventIcon.png);
  background-size: 73%;
  background-repeat: no-repeat;
  background-position: center center;
  height:75px;
  display: flex;
  justify-content: center;
  flex-direction: column;
}

.item .iconbg span{
  color:#fff;
  font-weight:700; 
  display: block;
}

.item .content{
  line-height:75px;
  padding-left:15px;
  text-transform: uppercase;
  font-weight:700;
  font-size:80%;
}

.feed_header div { width:33%; }



.bigDate{
	width:100px;
	height:100px;
	margin:0 auto;
	margin-top:40px;
}

.bigDate .iconbg{
  background-image: url(../../assets/eventIcon.png);
  background-size: 73%;
  background-repeat: no-repeat;
  background-position: center center;
  height:100px;
  display: flex;
  justify-content: center;
  flex-direction: column;
}

.bigDate .iconbg span{
  color:#fff;
  font-weight:700; 
  display: block;
  text-align:center;
  font-size:19px;
}

h2.eventdetail{
	font-size:100%;
	text-align:center;
  font-weight:700;
  max-width: 80%;
  margin: 0 auto;
  margin-top: 15px;
}

p.eventdetail{
	width:70%;
	text-align:center;
	margin:0 auto;
	font-size:70%;
	line-height:100%;
}

div.addcal{
	font-size:70%;
	width:140px;
	padding-top:5px;
	padding-bottom:5px;
	text-align:center;
	color:#fff;
	display:block;
	margin:0 auto;
	margin-top:20px;
}

.back{
  position: absolute;
  top: 7px;
  left: 20px;
  width: 0 !important; 
  height: 0; 
  border-top: 10px solid transparent;
  border-bottom: 10px solid transparent; 
  border-right:10px solid blue; 
}