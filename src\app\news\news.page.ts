import { Component, OnInit } from '@angular/core';
import { GenericService } from '../generic.service';

import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { Storage } from '@ionic/storage';
import { Platform } from '@ionic/angular';
import { ToastService } from '../service/toast.service';
import { HttpService } from '../service/http.service';


@Component({
  selector: 'app-news',
  templateUrl: './news.page.html',
  styleUrls: ['./news.page.scss'],
})
export class NewsPage implements OnInit {

  news: any;
  showLoader:any =true
  showMessage:any = false

  constructor( 
    public platform:Platform, 
    public generic: GenericService, 
    private storage: Storage,
    public toaster: ToastService,
    public httpService: HttpService
    ){
      // generic.setup();
  }
    
  ngOnInit() {
    this.getAllNews()
  }

  getAllNews(){
    this.storage.get('schoolid').then((val) => {
      this.httpService.getAllRecords(val, 'news').subscribe((response:any)=>{
        if(response.status == 200){
          if(response.data && response.data.length > 0){
            this.news = response.data;
            this.showLoader = false
          } else {
            this.showLoader = false
            this.showMessage = true
          }
        } else {
          this.showMessage = true
          this.showLoader = false
        }
      }, err=>{
        this.showMessage = true
        this.toaster.showToaster("Something went wrong!", 'danger')
        this.showLoader = false
      });
    });
  }

}
