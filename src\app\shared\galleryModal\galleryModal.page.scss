.ion-content-height {
  position: relative;
  background-color: #000;
  padding: 10px;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;

  .close {
    position: absolute;
    top: 30px;
    right: 7px;
    z-index: 9999;
    background: none;
    padding: 10px;
    
    ion-icon {
        color: #fff;
        font-size: 30px;
    }
  }

  .swiper-slide {
      height: calc(100vh - 20px);
  }

  .button {
      position: absolute;
      bottom: 10px;
      background: none;
      z-index: 9999;
      padding: 10px;

      ion-icon {
        color: #fff;
        font-size: 40px;
      }

      &.left {
          left: 0;
          transform: rotate(180deg);
      }

      &.right {
          right: 0;
      }
  }
}
