<app-header></app-header>

<ion-content class=" ion-content-height">

<div class="feed_header">
  <div (click)="generic?.loadTile('home','#')">
    <div class="back" [ngStyle]="{ 'border-right' : '10px solid ' + generic.secondColor }"></div>
  </div>
  <div>News</div>
  <div>&nbsp;</div>
</div>

<div class="feed" >
  
  <ion-card routerLink="/news-detail/ {{ newsItem.id }}" routerDirection="root" *ngFor="let newsItem of news; let i=index;">
    <ion-card-header>

      <div class="topnews">
      <div class="title" [ngStyle]="{ 'color' : generic.firstColor }">
          <span class="circleNotification" id="news{{ newsItem.id }}" *ngIf="generic?.isNewsUnread(newsItem.id)"></span>
          {{ newsItem?.name }}</div>
      <div class="date"> {{ newsItem?.datestamp }}</div>
      </div>
    </ion-card-header>

    <ion-card-content [innerHTML]="newsItem.snippet">
    </ion-card-content>
  </ion-card>
 
</div>
<div class="no-data-style" *ngIf="showMessage">
  <p>No data found!</p>
</div>
<div *ngIf="showLoader" class="loader-style">
  <ion-spinner name="crescent" style="color: #158145;"></ion-spinner>
</div>
</ion-content>
