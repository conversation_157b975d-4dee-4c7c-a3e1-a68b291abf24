import { OneSignal } from '@ionic-native/onesignal/ngx';
import { NavController } from '@ionic/angular';

import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { Storage } from '@ionic/storage';
import { Platform } from '@ionic/angular';
import { AlertController } from '@ionic/angular';
import * as angular from "@ionic/angular";
import { THIS_EXPR } from '@angular/compiler/src/output/output_ast';
import { Router, NavigationExtras } from '@angular/router';
import { environment } from 'src/environments/environment';
import { InAppBrowser, InAppBrowserOptions } from '@ionic-native/in-app-browser/ngx';

@Injectable({
   providedIn: 'root'
})
export class GenericService {

   public inAppBrowserOption: InAppBrowserOptions = {
      zoom: "no",
      closebuttoncaption: "Close",
      location: "no",
      usewkwebview: "no",
      fullscreen: "no",
      toolbar: 'yes'
   }

   public firstColor = '#1e2b5b';
   public secondColor = '#fcb616';

   public detailid = 0;
   public appinfo: any;
   public appid = 0;

   public publicyeargroups: any;
   public url: any;

   public playerid: any;
   public yeargroups = [];
   public myGroups = [];

   public yeargroupssaved = [];

   public unreadNewslettersIDS = [];
   public unreadEventsIDS = [];
   public unreadNewsIDS = [];
   public unreadLettersIDS = [];


   public unreadNewslettersCount = 0;
   public unreadEventsCount = 0;
   public unreadNewsCount = 0;
   public unreadLettersCount = 0;

   public notificationDetailID = 0;


   constructor(private router: Router, public alertController: AlertController, public platform: Platform, public oneSignal: OneSignal, public storage: Storage, public navCtrl: NavController, private http: HttpClient, private inAppBrowser: InAppBrowser) {
      /* Observable.interval(1500).subscribe((val) => {

          //this.loadPage('/news-detail/' + this.notificationDetailID);

         if(this.notificationDetailID != 0){
            this.loadPage('/news-detail/' + this.notificationDetailID);
            this.notificationDetailID = 0;
         }
      });

      */
   }

   setID(id) {
      this.appid = id;
   }

   setup() {
      let url1 = `${environment.baseUrl}/?key=trickydicky&school_id=` + this.appid + '&feed=appinfo';
      this.http.get(url1).subscribe((response) => {
         this.appinfo = response['data'];
         this.firstColor = this.appinfo.firstColor;
         this.secondColor = this.appinfo.secondColor;
      }, err => {
         console.log("App info api error", err)
      });

      this.storage.get('alert_newsletters').then((val) => {
         if (val != '') {

            var tempArray = [0];

            val.forEach(element => {
               let url1 = `${environment.baseUrl}/?key=trickydicky&type=newsletters&school_id=` + this.appid + '&feed=delete-check&id=' + element;
               this.http.get(url1).subscribe((response) => {
                  var data = response['data'];

                  if (data.result == 1) {
                     tempArray.push(element);
                  }
               });
            });

            this.storage.set('alert_newsletters', tempArray);
            this.unreadNewslettersCount = tempArray.length;
            //this.unreadNewslettersCount = val.length;
            this.unreadNewslettersCount--;
         } else {
            this.unreadNewslettersCount = 0;
         }
      });

      this.storage.get('alert_letters').then((val) => {
         if (val != '') {
            var tempArray = [0];
            val.forEach(element => {
               let url1 = `${environment.baseUrl}/?key=trickydicky&type=letters&school_id=` + this.appid + '&feed=delete-check&id=' + element;
               this.http.get(url1).subscribe((response) => {
                  var data = response['data'];

                  if (data.result == 1) {
                     tempArray.push(element);
                  }
               });
            });

            this.storage.set('alert_letters', tempArray);
            this.unreadLettersCount = tempArray.length;
            //this.unreadLettersCount = val.length;
            this.unreadLettersCount--;
         } else {
            this.unreadLettersCount = 0;
         }
      });

      this.storage.get('alert_news').then((val) => {
         if (val != '') {

            var tempArray = [0];

            val.forEach(element => {
               let url1 = `${environment.baseUrl}/?key=trickydicky&type=news&school_id=` + this.appid + '&feed=delete-check&id=' + element;
               this.http.get(url1).subscribe((response) => {
                  var data = response['data'];

                  if (data.result == 1) {
                     tempArray.push(element);
                  }
               });
            });

            this.storage.set('alert_news', tempArray);
            this.unreadNewsCount = tempArray.length;

            //this.unreadNewsCount = val.length;
            this.unreadNewsCount--;
         } else {
            this.unreadNewsCount = 0;
         }
      });


      this.storage.get('alert_events').then((val) => {
         if (val != '') {

            var tempArray = [0];
            val.forEach(element => {
               let url1 = `${environment.baseUrl}/?key=trickydicky&type=event&school_id=` + this.appid + '&feed=delete-check&id=' + element;
               this.http.get(url1).subscribe((response) => {
                  var data = response['data'];

                  if (data.result == 1) {
                     tempArray.push(element);
                  }
               });
            });

            this.storage.set('alert_events', tempArray);
            this.unreadEventsCount = tempArray.length;
            //this.unreadEventsCount = val.length;
            this.unreadEventsCount--;
         } else {
            this.unreadEventsCount = 0;
         }
      });


      this.storage.get('alert_newsletters').then((val) => {
         if (val != '') {
            this.unreadNewslettersIDS = val;
         } else {
            this.unreadNewslettersIDS = [];
         }
      });

      this.storage.get('alert_news').then((val) => {
         if (val != '') {
            this.unreadNewsIDS = val;
         } else {
            this.unreadNewsIDS = [];
         }
      });

      this.storage.get('alert_letters').then((val) => {
         if (val != '') {
            this.unreadLettersIDS = val;
         } else {
            this.unreadLettersIDS = [];
         }
      });

      this.storage.get('alert_events').then((val) => {
         if (val != '') {
            this.unreadEventsIDS = val;
         } else {
            this.unreadEventsIDS = [];
         }
      });

      var pls = this;


      this.platform.ready().then(() => {

         /*
         this.oneSignal
            .startInit("************************************", "881995178309")
            .inFocusDisplaying(this.oneSignal.OSInFocusDisplayOption.Notification)
            .handleNotificationOpened(function (jsonData) {

               if (jsonData.payload.additionalData.template == 'newsletter') {
                  pls.unreadNewslettersIDS.push(jsonData.payload.additionalData.detailid);
                  pls.storage.set('alert_newsletters', pls.unreadNewslettersIDS);
                  pls.unreadNewslettersCount++;

                  //pls.loadPage('/newsletters/');
                  pls.navCtrl.navigateRoot('/newsletters/');

               } else if (jsonData.payload.additionalData.template == 'news') {
                  //pls.unreadNewsIDS.push(jsonData.payload.additionalData.detailid);
                  // pls.storage.set('alert_news', pls.unreadNewsIDS);
                  // pls.unreadNewsCount++;
                  
                  pls.notificationDetailID = jsonData.payload.additionalData.detailid;

               } else if (jsonData.payload.additionalData.template == 'event') {
                  pls.unreadEventsIDS.push(jsonData.payload.additionalData.detailid);
                  pls.storage.set('alert_events', pls.unreadEventsIDS);
                  pls.unreadEventsCount++;

                  //pls.loadPage('/event-detail/' + jsonData.payload.additionalData.detailid);
                  pls.navCtrl.navigateRoot('/event-detail/' + jsonData.payload.additionalData.detailid);

               }

            })
            .handleNotificationReceived(function (jsonData) {

               if (jsonData.payload.additionalData.template == 'newsletter') {

                  pls.unreadNewslettersIDS.push(jsonData.payload.additionalData.detailid);
                  pls.storage.set('alert_newsletters', pls.unreadNewslettersIDS);
                  pls.unreadNewslettersCount++;

               } else if (jsonData.payload.additionalData.template == 'news') {
                  pls.unreadNewsIDS.push(jsonData.payload.additionalData.detailid);
                  pls.storage.set('alert_news', pls.unreadNewsIDS);
                  pls.unreadNewsCount++;
                  
                  pls.notificationDetailID = jsonData.payload.additionalData.detailid;


               } else if (jsonData.payload.additionalData.template == 'event') {
                  pls.unreadEventsIDS.push(jsonData.payload.additionalData.detailid);
                  pls.storage.set('alert_events', pls.unreadEventsIDS);
                  pls.unreadEventsCount++;

               }


            })
            .endInit();
            //this.oneSignal.setLogLevel({logLevel: 4, visualLevel: 4});
            this.oneSignal.getIds().then((id) => { this.playerid = id.userId; });

            */
      });


      var url = `${environment.baseUrl}/?key=trickydicky&school_id=` + this.appid + '&feed=yeargroups';
      this.http.get(url).subscribe((response) => {
         this.publicyeargroups = response['data'];
      });
      this.getNotificationGroups();
   }

   isNewsUnread(id) {
      var theReturn = false;

      if (this.unreadNewsIDS != null) {
         this.unreadNewsIDS.forEach(function (value, key) {

            if (value == id) {
               theReturn = true;
               console.log('its in: ' + id);
            }
         });
      } else {
         theReturn = false;
      }
      return theReturn;
   }

   isLetterUnread(id) {
      var theReturn = false;

      if (this.unreadLettersIDS != null) {
         this.unreadLettersIDS.forEach(function (value, key) {

            if (value == id) {
               theReturn = true;
               console.log('its in: ' + id);
            }
         });
      } else {
         theReturn = false;
      }

      return theReturn;
   }


   markLetterAsRead(id) {
      var element = document.getElementById('letter' + id);
      if (element != null) {
         element.remove();
      }

      var localArray = [];

      this.unreadNewsIDS.forEach(function (value, key) {
         if (value != id) {
            localArray.push(value);
         }
      });

      this.storage.set('alert_letters', localArray);
   }

   isEventUnread(id) {
      var theReturn = false;

      if (this.unreadEventsIDS != null) {
         this.unreadEventsIDS.forEach(function (value, key) {
            if (value == id) {
               theReturn = true;
               console.log('its in: ' + id);
            }
         });
      } else {
         theReturn = false;
      }
      return theReturn;
   }


   isNewsletterUnread(id) {
      var theReturn = false;

      if (this.unreadNewslettersIDS != null) {
         this.unreadNewslettersIDS.forEach(function (value, key) {

            if (value == id) {
               theReturn = true;
               console.log('its in: ' + id);
            }
         });
      } else {
         theReturn = false;
      }
      return theReturn;
   }

   markNewsletterAsRead(id) {
      var element = document.getElementById('newsletter' + id);
      if (element != null) {
         element.remove();
      }

      var localArray = [];

      this.unreadNewslettersIDS.forEach(function (value, key) {
         if (value != id) {
            localArray.push(value);
         }
      });
      this.storage.set('alert_newsletters', localArray);
   }

   markNewsAsRead(id) {
      var element = document.getElementById('news' + id);
      if (element != null) {
         element.remove();
      }

      var localArray = [];

      this.unreadNewsIDS.forEach(function (value, key) {
         if (value != id) {
            localArray.push(value);
         }
      });
      this.storage.set('alert_news', localArray);
   }

   markEventAsRead(id) {
      var element = document.getElementById('event' + id);
      if (element != null) {
         element.remove();
      }

      var localArray = [];

      this.unreadEventsIDS.forEach(function (value, key) {
         if (value != id) {
            localArray.push(value);
         }
      });

      this.storage.set('alert_events', localArray);
   }

   loadPage(url) {
      this.navCtrl.navigateRoot(url);
   }

   loadDetailPage(path, id) {
      this.router.navigate([path, id]);
   }

   loadTile(template, link, idd = 0) {
      if (template == 'home') {
         this.navCtrl.navigateRoot('/home');
      } else if (template == 'events') {
         this.navCtrl.navigateRoot('/event-list');
      } else if (template == 'event-detail') {
         this.navCtrl.navigateRoot(['/event-detail']);
         this.markEventAsRead(idd);
         this.detailid = idd;
      } else if (template == 'news') {
         this.navCtrl.navigateRoot(['/news']);
      } else if (template == 'news-detail') {
         this.navCtrl.navigateRoot(['news-detail']);
         this.markNewsAsRead(idd);
         this.detailid = idd;
      } else if (template == 'newsletters') {
         this.navCtrl.navigateRoot(['/newsletters']);
      } else if (template == 'letters') {
         this.navCtrl.navigateRoot(['/letters']);
      } else if (template == 'gallery') {
         this.navCtrl.navigateRoot(['/gallery']);
      } else if (template == 'gallery-detail') {
         this.navCtrl.navigateRoot(['/gallery-detail']);
         this.detailid = idd;
      } else if (template == 'settings') {
         this.navCtrl.navigateRoot(['/settings']);
      } else if (template == 'contact') {
         this.navCtrl.navigateRoot(['/contact']);
      } else if (template == 'link') {
         // window.open(link, '_system');
         // window.screen.orientation.lock('landscape')
         if (this.platform.is('ios')) {
            let browser = this.inAppBrowser.create(link, '_blank', this.inAppBrowserOption);
            browser.show();
         } else {
            let browser = this.inAppBrowser.create(link, '_blank', this.inAppBrowserOption);
            browser.show();
         }
      }
   }


   getNotificationGroups() {
      this.storage.get('player_id').then((player_id) => {
         var url = `${environment.baseUrl}/?key=trickydicky&school_id=` + this.appid + '&feed=myyeargroups&player_id=' + player_id;
         this.http.get(url).subscribe((response) => {
            this.myGroups = response['data'];
            for (var i = 0; i < this.myGroups.length; i++) {
               this.yeargroups[this.myGroups[i].id] = true;
            }
         }, (error) => {
         });
         //console.log(this.myGroups);
         /*
         for(const [key,value] of Object.entries(this.myGroups)){
            this.yeargroups[key] = true; 
         }*/
      });
   }

   saveSettings() {
      let yg = '';

      //console.log(this.yeargroups);

      for (const [key, value] of Object.entries(this.yeargroups)) {
         //console.log(key + ' / ' + value);

         if (value == true) {
            yg += key + ',';

         }
      }
      if (!this.playerid) {
         alert(`We couldn't retrieve your settings at this time. Please try again later`);
         return;
      }
      this.http.get(`${environment.baseUrl}/notificationAPI.php?mode=POST&player_id=` + this.playerid + '&school_id=' + this.appid + '&yeargroups=' + yg).subscribe((response) => {

      });

      this.saveAlert();
   }

   async saveAlert() {
      const alert = await this.alertController.create({
         header: 'Settings',
         message: 'Your selected year groups have been saved.',
         buttons: [{
            text: 'Ok',
            handler: () => {
               this.navCtrl.navigateRoot(['/home']);
            }
         }]
      });

      await alert.present();
   }

   loadNewsletter(newsletterid, link) {
      this.markNewsletterAsRead(newsletterid);
      if (this.platform.is('ios')) {
         let browser = this.inAppBrowser.create(link, '_blank', this.inAppBrowserOption);
         browser.show();
      } else {
         if (link.match(".pdf")) {
            let browser = this.inAppBrowser.create(link, '_system');
            browser.show();
         } else {
            let browser = this.inAppBrowser.create(link, '_blank', this.inAppBrowserOption);
            browser.show();
         }
      }
   }

   loadLetters(letterid, link) {
      this.markLetterAsRead(letterid);
      if (this.platform.is('ios')) {
         let browser = this.inAppBrowser.create(link, '_blank', this.inAppBrowserOption);
         browser.show();
      } else {
         if (link.match(".pdf")) {
            let browser = this.inAppBrowser.create(link, '_system');
            browser.show();
         } else {
            let browser = this.inAppBrowser.create(link, '_blank', this.inAppBrowserOption);
            browser.show();
         }
      }
   }

   resetApp() {
      this.storage.set('schoolid', 'a');
      this.navCtrl.navigateRoot(['/search']);
   }


}
