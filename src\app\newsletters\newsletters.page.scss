/************/
header{
  width:100%;
  height:250px;
  background: url("../../assets/phenix_splatter.png") no-repeat, #0a465f;
  background-size: cover;
  background-position: center center;
  position: relative;

}

.homeIcon{
  position: absolute;
  top: 45px;
  left: 20px;
  width: 30px;
}

.settingsIcon{
  position: absolute;
  top: 45px;
  right: 20px;
  width: 30px;
}


.schoolLogo{
  width: 105px;
  height: 105px;
  border-radius: 100%;
  margin: 0 auto;
  display: block;
  position: absolute;
  left:50%;
  top:50%;
  transform: translateY(-50%) translateX(-50%);
  background-color: white;
  padding: 5px;
}

.schoolText{
  position: absolute;
  bottom:20px;
  width:100%;
  left:50%;
  transform:translateX(-50%);
}

h2.schoolName{
  text-align: center;
  font-size: 78%;
  color: #fff;
  font-weight: 700;
  padding-top: 24px;
  margin:0;
  text-transform: uppercase;
}

h2.motto{
  text-align: center;
  font-size: 50%;
  color: #fff;
  margin: 0px;
  padding: 0px;
  padding-top:7px;
}
/************/



div.tile{
  width:100%;
  display: flex;
  justify-content: flex-start;
}

div.tile .icon{
  width: 75px;
  height: 76px;
}

div.tile .icon img{
  width: 35px;
  margin: 0 auto;
  display: block;
  padding-top: 20px;
}

div.tile .content{
  width: calc(100% - 75px);
  height: auto;
  background: #ffffff;
  line-height: 75px;
  padding-left: 15px;
  border-bottom: 1px solid #aaaaaa;
  color:#0a465f;
  text-transform: uppercase;
  font-weight:700;
  font-size:80%;
  line-height: 140%;

  display:flex;
  align-items: center;
  justify-content: flex-start;
}

.pulltab{
  width:100%;
  border-bottom:8px solid #1e2b5b;
  position: absolute;
  bottom:0px;
  left:0px;
}

.pulltab .tab{
  background: #1e2b5b;
  width: 100px;
  height: 17px;
  margin: 0 auto;
  border-top-left-radius: 6px;
  border-top-right-radius: 6px;
}


::-webkit-scrollbar, *::-webkit-scrollbar { display: none; }

a { text-decoration:none; }

div.feed_header{
	text-align: center;
  background: #eeeeee;
  color: #0a465f;
  text-transform: uppercase;
  font-weight: 700;
  padding-top: 10px;
  padding-bottom: 10px;
  font-size: 80%;
  display: flex;
  justify-content: space-evenly;
}
 
.feed_header div { width:33%; }
div.feed .item{
	width:100%;
	display:flex;
  border-bottom:1px solid #aaaaaa;
}



.back{
  position: absolute;
  top: 7px;
  left: 20px;
  width: 0 !important; 
  height: 0; 
  border-top: 10px solid transparent;
  border-bottom: 10px solid transparent; 
  border-right:10px solid blue; 
}



.content .circleNotification{
  background: #f00f00;
  width: 20px;
  height: 20px;
  border-radius: 100%;
  display: block;
  line-height: 20px;
  text-align: center;
  color: white;
  margin-right:7px;
}
