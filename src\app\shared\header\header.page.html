<header [ngStyle]="{ 'background-color' : generic?.firstColor, 'border-bottom' : '3px solid ' + generic?.secondColor}">
    <img [src]="'http://www.phenixapps.co.uk/assets/'+ generic?.appinfo?.logo" class="schoolLogo">
    <img src="/assets/home.png" class="homeIcon" (click)="generic?.loadTile('home', '#')">
    <img src="/assets/setting.png" class="settingsIcon" (click)="generic?.loadTile('settings', '#')">
    <div class="schoolText">
    <h2 class="schoolName animated fadeIn">{{ generic?.appinfo?.name }}</h2>
    <h2 class="motto animated fadeIn">THE PARENT APP</h2>
    </div>
  </header>