import { Component, OnInit } from '@angular/core';
import { GenericService } from '../generic.service';

import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { Storage } from '@ionic/storage';
import { CallNumber } from '@ionic-native/call-number/ngx';
import { HttpService } from '../service/http.service';
import { ToastService } from '../service/toast.service';

@Component({
  selector: 'app-contact',
  templateUrl: './contact.page.html',
  styleUrls: ['./contact.page.scss'],
})
export class ContactPage implements OnInit {

  showLoader:any =true
  showMessage:any = false

  public contactinfo: any;
  constructor(private callNumber: CallNumber, 
    public generic: GenericService, 
    private storage: Storage, 
    private httpSerice: HttpService,
    public toaster: ToastService
  ) {
    // generic.setup();
  }
  
  getContact(){
    this.storage.get('schoolid').then((val) => {
      this.httpSerice.getAllRecords(val, 'contact').subscribe((response:any) => {
        if(response.status == 200){
          if(response.data && response.data.length > 0){
            this.contactinfo = response.data[0];
            this.showLoader = false
          } else {
            this.showLoader = false
            this.showMessage = true
          }
        } else {
          this.showLoader = false
          this.showMessage = true
        }
      }, err=>{
        this.showMessage = true
        this.toaster.showToaster("Something went wrong!", 'danger')
        this.showLoader = false
      });
    })
  }

  openMaps(){
    //window.open('geo://52.60199,-2.12713', '_system');
  }

  makeEmail(text){
    window.open('mailto:' + text, '_system')
  }

  makePhonecall(number){
    this.callNumber.callNumber(number, true)
    .then(res => console.log('Launched dialer!', res))
    .catch(err => console.log('Error launching dialer', err));
  }

  ngOnInit() {
    this.getContact()
  }

}
