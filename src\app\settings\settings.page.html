<app-header></app-header>

<ion-content class=" ion-content-height">

<div class="feed_header">
    <div (click)="generic.loadTile('home','#')">
      <div class="back" [ngStyle]="{ 'border-right' : '10px solid ' + generic.secondColor }"></div>
    </div>
  <div>Settings</div>
  <div>&nbsp;</div>
</div>

<div class="settingsfeed">
  <p class="prev">Select year groups from the list below to keep up to date and receive notifications</p>
    
  <div style="padding-left:15px;padding-right:15px;">
  <div class="strip" *ngFor="let year of generic.publicyeargroups; let i=index;">
    <div>{{ year.name }}</div>

    
    <div style="margin-top:-10px;">
      <ion-toggle color="primary" [(ngModel)]="generic.yeargroups[year.id]"></ion-toggle>
    </div>
  </div>

  <div (click)="generic.saveSettings()" class="resetbtn" [ngStyle]="{ 'background-color' : generic.firstColor}">Save Settings</div>


  <div (click)="generic.resetApp()" class="resetbtn" [ngStyle]="{ 'background-color' : generic.secondColor}">Change School</div>

</div>
</div>
</ion-content>
