<app-header></app-header>

<ion-content>

<div class="feed_header">
  <div (click)="generic?.loadTile('home','#')">
    <div class="back" [ngStyle]="{ 'border-right' : '10px solid ' + generic?.secondColor }"></div>
  </div>
  <div>Contact</div>
  <div>&nbsp;</div>
</div>
<div class="circles">
  <div class="circle" (click)="openMaps()" [ngStyle]="{ 'background-color' : generic?.firstColor }" style="display:none;">
    <img src="/assets/location.png" height="28">
  </div>
  <div class="circle" (click)="makeEmail(contactinfo?.email)" [ngStyle]="{ 'background-color' : generic?.firstColor }">
    <img src="/assets/email-icon.png" height="22">
  </div>
  <div class="circle" (click)="makePhonecall(contactinfo?.phonenumber)" [ngStyle]="{ 'background-color' : generic?.firstColor }">
    <img src="/assets/phone.png" height="22">
  </div>
</div>

<div class="feed_header" style="margin-top:30px;">
  <div></div>
  <div>ADDRESS</div>
  <div>&nbsp;</div>
</div>
  <br/>
    <div class="aboutBox">
      <p class="address" [ngStyle]="{ 'color' : generic.firstColor }">{{ contactinfo?.shorttext }}</p>
    </div>
    <br/>
    <div class="no-data-style" *ngIf="showMessage">
      <p>No data found!</p>
      </div>
    <div *ngIf="showLoader" class="loader-style">
      <ion-spinner name="crescent" style="color: #158145;"></ion-spinner>
    </div>
</ion-content>