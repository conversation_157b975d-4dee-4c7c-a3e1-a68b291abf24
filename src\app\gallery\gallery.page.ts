import { Component, OnInit } from '@angular/core';
import { GenericService } from '../generic.service';

import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { Storage } from '@ionic/storage';
import { HttpService } from '../service/http.service';
import { ToastService } from '../service/toast.service';

@Component({
  selector: 'app-gallery',
  templateUrl: './gallery.page.html',
  styleUrls: ['./gallery.page.scss'],
})
export class GalleryPage implements OnInit {

  galleries:any;
  showLoader:any =true
  showMessage:any = false

  constructor(public generic: GenericService, 
    private storage: Storage, 
    private httpService: HttpService,
    private toaster: ToastService
  ) {
    // generic.setup();
  }
  
  ngOnInit() {
    this.getGalleries()
  }
  
  getGalleries(){
    this.storage.get('schoolid').then((val) => {
      this.httpService.getAllRecords(val, 'gallery').subscribe((response:any) => {
        if(response.status == 200){
          if(response.data && response.data.length > 0){
            this.galleries = response.data;
            this.showLoader = false
          } else {
            this.showLoader = false
            this.showMessage = true
          }
        } else {
          this.showLoader = false
          this.showMessage = true
        }
      }, err=>{
          this.showMessage = true
            this.toaster.showToaster("Something went wrong!", 'danger')
            this.showLoader = false
      });
    });
  };

  galleryImage(image){
    let placeholderImg = '../../assets/placeholder.png';
    if(this.generic && this.generic.appinfo && this.generic.appinfo.website_type){
      if(this.generic.appinfo.website_type === 'wp'){
        return image.dfile ? image.dfile : placeholderImg
      }
      if(this.generic.appinfo.website_type === 'php'){
        return image.dfile ? this.generic.appinfo.url + 'images/image_gallery/'+ image.dfile : placeholderImg
      }
    }else{
      return placeholderImg;
    }
  }

}
