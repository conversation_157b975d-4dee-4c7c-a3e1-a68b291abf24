content{
	background:  #0d4b78 url('../../assets/splash.png') 50% 98% no-repeat;
	height:100%;
	width:100%;
	background-size: 100%;
	animation: pulse 1.4s;

}



@-webkit-keyframes pulse {
	0% {
	  -webkit-transform: scale(1);
	}
	50% {
	  -webkit-transform: scale(1.1);
	}
	100% {
	  -webkit-transform: scale(1);
	}
  }
  
  @keyframes pulse {
	0% {
	  transform: scale(1);
	}
	50% {
	  transform: scale(1.1);
	}
	100% {
	  transform: scale(1);
	}
  }

  
.search{
	
	width: 70%;
    height: 35px;
    margin-top: 60px;
	border:0px;
	outline:none;
	padding-left:45px;
	padding-right:45px;
	background:#fff;
	
	/*
	height: 35px;
    margin-top: 60px;
    border: 0px;
    outline: none;
    padding-left: 45px;
    padding-right: 45px;
    background: #fff;
    margin-left: 10%;
    margin-right: 10%;
    position: fixed;
    width: 80%;
    left: 0px;
    right: 0px;*/
}

.searchContainer{
	width:100%;
	margin:0 auto;
	text-align:center;
	position: relative;
}

img.asdf{
	position: absolute;
    left: 18%;
    top: 68px;
    width: 20px;
}

.searchResults{
	background: #fff;
	// display:none;
	min-height:60%;
	margin-top:30px;
	overflow-y:scroll;
}
.searchStrip{
	background: #e5e5e5;
	padding:5px;
	text-align:center;
}

.school{
	padding:20px;
	display: flex;
	justify-content: flex-start;
	align-items: center;
}

.school div{
	padding-left:20px;
	color:#0e465f;
	font-weight:700;
	text-transform: uppercase;
}

.scroll-content{
	padding-bottom: 0 !important;
}