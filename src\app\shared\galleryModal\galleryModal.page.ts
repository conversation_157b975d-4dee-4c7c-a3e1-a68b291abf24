import { Component, Input, OnInit, ViewChild } from '@angular/core';
import { GenericService } from 'src/app/generic.service';
import { IonSlides, ModalController } from '@ionic/angular';
import { ViewController } from '@ionic/core';

@Component({
    selector: 'app-galleryModal',
    templateUrl: './galleryModal.page.html',
    styleUrls: ['./galleryModal.page.scss'],
})
export class GalleryModal implements OnInit {

    @Input() galleryData: any;
    @Input() imageIndex = 0
    galleryImages: any = []
    @ViewChild('slider', { static: true }) private slider: IonSlides;
    constructor(
        public generic: GenericService,
        public viewCtrl: ModalController,
    ) {
        generic.setup();
    }

    ngOnInit() {
        this.galleryImages = this.galleryData;
        this.slider.slideTo(this.imageIndex);
    }

    nextImage() {
        this.slider.slideTo(++this.imageIndex)
    }

    previousImage() {
        this.slider.slideTo(--this.imageIndex)
    }

    onClose() {
        this.viewCtrl.dismiss();
    }
}
