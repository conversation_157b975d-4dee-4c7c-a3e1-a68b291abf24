<app-header></app-header>

<ion-content>

<div class="feed_header">
  <div (click)="generic.loadTile('gallery','#')">
    <div class="back" [ngStyle]="{ 'border-right' : '10px solid ' + generic.secondColor }"></div>
  </div>
  <div>Gallery</div>
  <div>&nbsp;</div>
</div>

<div class="feed">
  <div class="galimage" 
    *ngFor="let image of galleryimages; let i=index;" 
    [ngStyle]="{'background-image': 'url(' + galleryImage(image) + ')'}" 
    (click)="openModal(i)"
  >
  </div>
</div>

  <!-- <div class="feed" *ngIf="generic.appinfo.website_type ==  'wp'">
    <div class="galimage" 
      *ngFor="let image of galleryimages; let i=index;" 
      [ngStyle]="{'background-image': 'url(' + image?.dfile + ')'}" 
      (click)="openModal(i)">
    </div>
  </div>

  <div class="feed" *ngIf="generic?.appinfo?.website_type == 'php'">
    <div class="galimage" *ngFor="let image of galleryimages; let i=index;" [ngStyle]="{'background-image': 'url(' + generic.appinfo.url + 'images/image_gallery/'+ image?.dfile + ')'}" (click)="openModal(i)"></div>
  </div> -->

  <div class="no-data-style" *ngIf="showMessage">
    <p>No data found!</p>
  </div>

  <div *ngIf="showLoader" class="loader-style">
    <ion-spinner name="crescent" style="color: #158145;"></ion-spinner>
  </div>

</ion-content>
