import { Component, OnInit } from '@angular/core';
import { GenericService } from '../generic.service';

import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { Storage } from '@ionic/storage';
import { HttpService } from '../service/http.service';

@Component({
  selector: 'app-settings',
  templateUrl: './settings.page.html',
  styleUrls: ['./settings.page.scss'],
})
export class SettingsPage implements OnInit {

  events: any;
  allSchoolRecords:any []
  constructor(public generic: GenericService, 
    public storage: Storage, 
    private httpSerice: HttpService
    ){
    generic.setup();
    if (generic.unreadNewslettersIDS == null) {
      this.storage.set('alert_newsletters', [0]);
    }

    if (generic.unreadNewsIDS == null) {
      this.storage.set('alert_news', [0]);
    }

    if (generic.unreadEventsIDS == null) {
      this.storage.set('alert_events', [0]);
    }

    if (generic.unreadLettersIDS == null) {
      this.storage.set('alert_letters', [0]);
    }

    storage.get('schoolid').then((val) => {
      console.log('settings.ts School id: ' + val);
      this.generic.appid = val;
    });
  }

  ngOnInit() {
    this.getSetting()
  }

  getSetting(){
    this.httpSerice.getAllRecords(this.generic.appid, 'tiles').subscribe((response:any) => {
      this.storage.set('tile-list', response.data);
    })
  }

  updateItem(item) {
    console.log(item);
  }
}
