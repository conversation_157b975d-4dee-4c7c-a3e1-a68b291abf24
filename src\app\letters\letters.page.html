<app-header></app-header>
  
  <ion-content class=" ion-content-height">
    
    <div class="feed_header">
      <div (click)="generic.loadTile('home','#')">
        <div class="back" [ngStyle]="{ 'border-right' : '10px solid ' + generic.secondColor }"></div>
      </div>
      
      <div>Letters</div>
      <div>&nbsp;</div>
    </div>
    
    <div class="tiles"> 
      <a *ngFor="let letter of letters; let i=index;">
      <div class="tile" (click)="generic.loadLetters(letter.id, generic.appinfo.url + 'letters/'+ letter.dfile)">
        <div class="icon animated fadeIn" [ngStyle]="{ 'background-color' : generic.firstColor}" *ngIf="letter.row">
          <img src="../../assets/iconn.png">
        </div>
  
        <div class="icon animated fadeIn" [ngStyle]="{ 'background-color' : generic.secondColor}" *ngIf="letter.row == false">
          <img src="../../assets/iconn.png">
        </div>
  
        <div class="content">
          <span class="circleNotification" id="letter{{ letter.id }}" *ngIf="generic.isLetterUnread(letter.id)"></span>
          {{ letter.name }}
        </div>
      </div>
      </a>
    </div>
    <div class="no-data-style" *ngIf="showMessage">
      <p>No data found!</p>
    </div>
    <div *ngIf="showLoader" class="loader-style">
      <ion-spinner name="crescent" style="color: #158145;"></ion-spinner>
    </div>
  </ion-content>
  