import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Routes, RouterModule } from '@angular/router';

import { IonicModule } from '@ionic/angular';

import { NewslettersPage } from './newsletters.page';
import { HeaderPage } from '../shared/header/header.page';

const routes: Routes = [
  {
    path: '',
    component: NewslettersPage
  }
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    RouterModule.forChild(routes)
  ],
  declarations: [NewslettersPage, HeaderPage]
})
export class NewslettersPageModule {}
