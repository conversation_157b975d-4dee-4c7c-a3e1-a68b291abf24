{"name": "school-app", "version": "0.0.1", "author": "Ionic Framework", "homepage": "https://ionicframework.com/", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e"}, "dependencies": {"@angular/common": "^9.1.11", "@angular/core": "^9.1.11", "@angular/forms": "^9.1.11", "@angular/platform-browser": "^9.1.11", "@angular/platform-browser-dynamic": "^9.1.11", "@angular/router": "^9.1.11", "@capacitor/core": "2.1.2", "@capacitor/ios": "^2.2.0", "@ionic-native/calendar": "^5.36.0", "@ionic-native/call-number": "^5.26.0", "@ionic-native/core": "^5.26.0", "@ionic-native/in-app-browser": "^5.5.0", "@ionic-native/onesignal": "^5.27.0", "@ionic-native/photo-viewer": "^5.29.0", "@ionic-native/screen-orientation": "^5.36.0", "@ionic-native/splash-screen": "^5.26.0", "@ionic-native/status-bar": "^5.26.0", "@ionic/angular": "^5.2.2", "@ionic/storage": "^2.2.0", "call-number": "^1.0.1", "com-sarriaroman-photoviewer": "^1.2.2", "cordova-android": "^10.1.1", "cordova-browser": "^6.0.0", "cordova-plugin-actionsheet": "^2.3.3", "cordova-plugin-advanced-http": "^2.5.1", "cordova-plugin-calendar": "^5.1.5", "cordova-plugin-device": "^2.0.3", "cordova-plugin-dialogs": "^2.0.2", "cordova-plugin-file": "^6.0.2", "cordova-plugin-ionic-keyboard": "^2.2.0", "cordova-plugin-ionic-webview": "^5.0.0", "cordova-plugin-splashscreen": "^5.0.4", "cordova-plugin-statusbar": "^2.4.3", "cordova-plugin-whitelist": "^1.3.4", "cordova-sqlite-storage": "^5.0.0", "jquery": "^3.5.1", "moment": "^2.29.1", "mx.ferreyra.callnumber": "0.0.2", "node-sass": "^4.14.1", "onesignal-cordova-plugin": "^2.11.0", "rxjs": "~6.5.1", "rxjs-compat": "^6.6.3", "tslib": "^1.10.0", "uk.co.workingedge.phonegap.plugin.launchnavigator": "^5.0.4", "zone.js": "~0.10.2"}, "devDependencies": {"@angular-devkit/build-angular": "^0.901.9", "@angular/cli": "^9.1.9", "@angular/compiler": "^9.1.11", "@angular/compiler-cli": "^9.1.11", "@angular/language-service": "^9.1.11", "@capacitor/cli": "2.1.2", "@ionic/angular-toolkit": "^2.1.1", "@types/jasmine": "~3.5.0", "@types/jasminewd2": "~2.0.3", "@types/node": "^12.12.47", "codelyzer": "^5.1.2", "cordova-ios": "^6.2.0", "cordova-plugin-inappbrowser": "git+https://github.com/apache/cordova-plugin-inappbrowser.git", "cordova-plugin-screen-orientation": "^3.0.2", "jasmine-core": "~3.5.0", "jasmine-spec-reporter": "~4.2.1", "karma": "^5.1.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage-istanbul-reporter": "~2.1.0", "karma-jasmine": "~3.0.1", "karma-jasmine-html-reporter": "^1.4.2", "protractor": "~5.4.3", "ts-node": "~8.3.0", "tslint": "~6.1.0", "typescript": "~3.8.3"}, "description": "An Ionic project", "cordova": {"platforms": ["browser", "android", "ios"], "plugins": {"cordova-plugin-device": {}, "cordova-plugin-splashscreen": {}, "cordova-plugin-advanced-http": {"OKHTTP_VERSION": "3.10.0"}, "cordova-sqlite-storage": {}, "cordova-plugin-statusbar": {}, "cordova-plugin-ionic-keyboard": {}, "com-sarriaroman-photoviewer": {}, "mx.ferreyra.callnumber": {}, "cordova-plugin-whitelist": {}, "uk.co.workingedge.phonegap.plugin.launchnavigator": {"GOOGLE_API_KEY_FOR_ANDROID": "881995178309", "LOCATION_USAGE_DESCRIPTION": "This app requires access to your location for navigation purposes", "OKHTTP_VERSION": "3.12.0"}, "cordova-plugin-ionic-webview": {"ANDROID_SUPPORT_ANNOTATIONS_VERSION": "27.+"}, "call-number": {}, "cordova-plugin-calendar": {"CALENDAR_USAGE_DESCRIPTION": " ", "CONTACTS_USAGE_DESCRIPTION": " "}, "cordova-plugin-inappbrowser": {}, "cordova-plugin-screen-orientation": {}, "onesignal-cordova-plugin": {}}}}