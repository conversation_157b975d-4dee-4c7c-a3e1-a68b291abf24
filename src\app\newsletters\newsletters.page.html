<app-header></app-header>

<ion-content class=" ion-content-height">
	
	<div class="feed_header">
		<div (click)="generic.loadTile('home','#')">
			<div class="back" [ngStyle]="{ 'border-right' : '10px solid ' + generic.secondColor }"></div>
		</div>
		
		<div>Newsletters</div>
		<div>&nbsp;</div>
	</div>
	
	<div class="tiles"> 
		<a *ngFor="let newsletter of newsletters; let i=index;">
		<div class="tile" (click)="generic.loadNewsletter(newsletter.id, generic.appinfo.url + 'newsletters/'+ newsletter.dfile)">
			<div class="icon animated fadeIn" [ngStyle]="{ 'background-color' : generic.firstColor}" *ngIf="newsletter.row">
				<img src="../../assets/iconn.png">
			</div>

			<div class="icon animated fadeIn" [ngStyle]="{ 'background-color' : generic.secondColor}" *ngIf="newsletter.row == false">
				<img src="../../assets/iconn.png">
			</div>

			<div class="content">
				<span class="circleNotification" id="newsletter{{ newsletter.id }}" *ngIf="generic.isNewsletterUnread(newsletter.id)"></span>
				{{ newsletter.name }}
			</div>
		</div>
		</a>
	</div>
	<div class="no-data-style" *ngIf="showMessage">
		<p>No data found!</p>
	  </div>
		<div *ngIf="showLoader" class="loader-style">
		  <ion-spinner name="crescent" style="color: #158145;"></ion-spinner>
		</div>
</ion-content>
