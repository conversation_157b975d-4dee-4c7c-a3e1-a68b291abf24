import { GenericService } from './generic.service';
import { Component, OnInit } from '@angular/core';

import { Platform, NavController } from '@ionic/angular';
import { SplashScreen } from '@ionic-native/splash-screen/ngx';
import { StatusBar } from '@ionic-native/status-bar/ngx';
import { OneSignal, OSNotificationOpenedResult } from '@ionic-native/onesignal/ngx';
import { Storage } from '@ionic/storage';
import { ScreenOrientation } from '@ionic-native/screen-orientation/ngx';
@Component({
	selector: 'app-root',
	templateUrl: 'app.component.html'
})
export class AppComponent implements OnInit {
	constructor(
		public oneSignal: OneSignal, 
		public storage: Storage, 
		private navCtrl: NavController, 
		private platform: Platform, 
		private splashScreen: SplashScreen, 
		private statusBar: StatusBar, 
		public generic: GenericService,
		private screenOrientation: ScreenOrientation
	) {
	}
	
	ngOnInit(): void {
		this.initializeApp();
	}
	
	initializeApp() {
		this.platform.ready().then(() => {
			this.setupNotifications();
			this.generic.setup();
			//this.screenOrientation.lock(this.screenOrientation.ORIENTATIONS.LANDSCAPE);
			this.splashScreen.hide();
			this.statusBar.backgroundColorByHexString('#083749');
			this.statusBar.styleLightContent();
			this.platform.backButton.subscribe(() => {
				this.navCtrl.back();
			});
			this.screenOrientation.unlock();
		});
	}
	setupNotifications() {
		var self = this;
		var notificationOpenedCallback = function (jsonData) {
			console.log('log notificationOpenedCallback!');
			//console.log('log notificationOpenedCallback! id: ' + jsonData.payload.additionalData.detailid);
			
			console.log('kez: ' + JSON.stringify(jsonData.notification.payload.additionalData.detailid));

			setTimeout(function () {
				console.log('log notificationOpenedCallback - delay! id: ' + jsonData.notification.payload.additionalData.detailid);
				
				if(jsonData.notification.payload.additionalData.template == 'event'){
					self.generic.loadDetailPage('event-detail', jsonData.notification.payload.additionalData.detailid);

					self.generic.unreadEventsIDS.push(jsonData.notification.payload.additionalData.detailid);
					self.generic.storage.set('alert_events', self.generic.unreadEventsIDS);
					self.generic.unreadEventsCount++;

				}else if(jsonData.notification.payload.additionalData.template == 'news'){
					self.generic.loadDetailPage('news-detail', jsonData.notification.payload.additionalData.detailid);

					self.generic.unreadNewsIDS.push(jsonData.notification.payload.additionalData.detailid);
					self.storage.set('alert_news', self.generic.unreadNewsIDS);
					self.generic.unreadNewsCount++;

				}else if(jsonData.notification.payload.additionalData.template == 'newsletter'){
					self.generic.loadPage('newsletters');
					
					self.generic.unreadNewslettersIDS.push(jsonData.notification.payload.additionalData.detailid);
					self.generic.storage.set('alert_newsletters', self.generic.unreadNewslettersIDS);
					self.generic.unreadNewslettersCount++;
				

				}else if(jsonData.notification.payload.additionalData.template == 'letter'){
					self.generic.loadPage('letters');

					self.generic.unreadLettersIDS.push(jsonData.notification.payload.additionalData.detailid);
					self.generic.storage.set('alert_letters', self.generic.unreadLettersIDS);
					self.generic.unreadLettersCount++;
				}
			 }, 2000);
		};


		this.oneSignal.startInit("************************************", "881995178309")
		
		this.oneSignal.handleNotificationOpened().subscribe((notification: OSNotificationOpenedResult) => {
			notificationOpenedCallback(notification)
		});
		this.oneSignal.handleNotificationReceived().subscribe((jsonData: any) => {
			alert(jsonData);
	
			if (jsonData.notification.payload.additionalData.template == 'newsletter') {
				self.generic.unreadNewslettersIDS.push(jsonData.notification.payload.additionalData.detailid);
				self.generic.storage.set('alert_newsletters', self.generic.unreadNewslettersIDS);
				self.generic.unreadNewslettersCount++;
			} else if (jsonData.notification.payload.additionalData.template == 'news') {
				self.generic.unreadNewsIDS.push(jsonData.notification.payload.additionalData.detailid);
				self.storage.set('alert_news', self.generic.unreadNewsIDS);
				self.generic.unreadNewsCount++;
	
				self.generic.notificationDetailID = jsonData.notification.payload.additionalData.detailid;
	
			} else if (jsonData.notification.payload.additionalData.template == 'event') {
				self.generic.unreadEventsIDS.push(jsonData.notification.payload.additionalData.detailid);
				self.generic.storage.set('alert_events', self.generic.unreadEventsIDS);
				self.generic.unreadEventsCount++;
	
			} else if (jsonData.notification.payload.additionalData.template == 'letter') {
				self.generic.unreadLettersIDS.push(jsonData.notification.payload.additionalData.detailid);
				self.generic.storage.set('alert_letters', self.generic.unreadLettersIDS);
				self.generic.unreadLettersCount++;
	
			}
	});
	
	
	this.oneSignal.endInit();

	this.oneSignal.getIds().then((id) => { 
		this.generic.playerid = id.userId;
		this.storage.set('player_id', id.userId);
	  }).catch(error => {
		console.error("onesignal error", error)
		// alert("We couldn't retrieve your settings at this time. Please try again later or contact support if the issue persists.");
	  });	
	}
}
