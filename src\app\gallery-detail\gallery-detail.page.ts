import { Component, OnInit } from '@angular/core';
import { GenericService } from '../generic.service';

import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { Storage } from '@ionic/storage';
import { PhotoViewer } from '@ionic-native/photo-viewer/ngx';
import { HttpService } from '../service/http.service';
import { ToastService } from '../service/toast.service';
import { ModalController } from '@ionic/angular';
import { GalleryModal } from '../shared/galleryModal/galleryModal.page';

@Component({
  selector: 'app-gallery-detail',
  templateUrl: './gallery-detail.page.html',
  styleUrls: ['./gallery-detail.page.scss'],
})
export class GalleryDetailPage implements OnInit {

  galleryimages:any;
  showLoader:any =true
  showMessage:any = false

  constructor(
    public photoViewer: PhotoViewer, 
    public generic: GenericService, 
    private storage: Storage, 
    private httpService: HttpService,
    private toaster: ToastService,
    public modalController: ModalController
    ) {
      // generic.setup();
  }

  ngOnInit() {
    this.getGalleryDetails()
  }

  async openModal(index){
    const modal = await this.modalController.create({
      component: GalleryModal,
      componentProps: { 
        galleryData: this.galleryimages,
        imageIndex: index
      },
      cssClass: 'h-image-popup-modal'
    });
    return await modal.present();
  }

  getGalleryDetails(){
    this.storage.get('schoolid').then((val) => {
      this.httpService.getDetails('gallery-detail', this.generic.appid, this.generic.detailid).subscribe((response:any) => {
        if(response.status == 200){
          if(response.data && response.data.length > 0){
            this.galleryimages = response.data;
            this.showLoader = false
          } else {
            this.showLoader = false
            this.showMessage = true
          }
        } else {
          this.showMessage = true
          this.showLoader = false
        }
      });
    }, err =>{
      this.showMessage = true
        	this.toaster.showToaster("Something went wrong!", 'danger')
        	this.showLoader = false
    });
  };

  galleryImage(image){
    let placeholderImg = '../../assets/placeholder.png';
    if(this.generic && this.generic.appinfo && this.generic.appinfo.website_type){
      if(this.generic.appinfo.website_type === 'wp'){
        return image.dfile ? image.dfile : placeholderImg
      }
      if(this.generic.appinfo.website_type === 'php'){
        return image.dfile ? this.generic.appinfo.url + 'images/image_gallery/'+ image.dfile : placeholderImg
      }
    }else{
      return placeholderImg;
    }
  }

}
