<app-header></app-header>

<ion-content (swipe)="swipeEvent()" class=" ion-content-height">

<div class="feed_header">
  <div (click)="generic.loadTile('home','#')">
    <div class="back" [ngStyle]="{ 'border-right' : '10px solid ' + generic.secondColor }"></div>
  </div>
  <div>Events</div>
  <div>&nbsp;</div>
</div>

<div class="feed">
  <div class="item" routerLink="/event-detail/{{event?.id}}" routerDirection="root" *ngFor="let event of events; let i=index;">
      <div class="date" [ngStyle]="{ 'background-color' : generic.firstColor }" *ngIf="event.row">
        <div class="iconbg">
          <span>{{ event?.dateD }} </span>
          <span>{{ event?.dateM }}</span>
         </div>
      </div>
      <div class="date" [ngStyle]="{ 'background-color' : generic.secondColor }" *ngIf="event.row == false">
        <div class="iconbg">
          <span>{{ event?.dateD }}</span>
          <span>{{ event?.dateM }}</span>
         </div>
      </div>
      <div class="content" [ngStyle]="{ 'color' : generic.firstColor}">
          <span class="circleNotification" id="event{{ event?.id }}" *ngIf="generic?.isEventUnread(event.id)"></span>
      <p>{{ event?.page_name }}</p>
    </div>
  </div>
</div>
<div class="no-data-style" *ngIf="showMessage">
  <p>No data found!</p>
</div>
<div *ngIf="showLoader" class="loader-style">
  <ion-spinner name="crescent" style="color: #158145;"></ion-spinner>
</div>
</ion-content>
