/************/
header{
  width:100%;
  height:250px;
  background: url("../../assets/phenix_splatter.png") no-repeat, #0a465f;
  background-size: cover;
  background-position: center center;
  position: relative;

}

.homeIcon{
  position: absolute;
  top: 45px;
  left: 20px;
  width: 30px;
}

.settingsIcon{
  position: absolute;
  top: 45px;
  right: 20px;
  width: 30px;
}

.schoolLogo{
  width: 105px;
  height: 105px;
  border-radius: 100%;
  margin: 0 auto;
  display: block;
  position: absolute;
  left:50%;
  top:50%;
  transform: translateY(-50%) translateX(-50%);
  background-color: white;
  padding: 5px;
}

.schoolText{
  position: absolute;
  bottom:20px;
  width:100%;
  left:50%;
  transform:translateX(-50%);
}

h2.schoolName{
  text-align: center;
  font-size: 78%;
  color: #fff;
  font-weight: 700;
  padding-top: 24px;
  margin:0;
  text-transform: uppercase;
}

h2.motto{
  text-align: center;
  font-size: 50%;
  color: #fff;
  margin: 0px;
  padding: 0px;
  padding-top:7px;
}
/************/
div.feed_header{
	text-align: center;
  background: #eeeeee;
  color: #0a465f;
  text-transform: uppercase;
  font-weight: 700;
  padding-top: 10px;
  padding-bottom: 10px;
  font-size: 80%;
  display: flex;
  justify-content: space-evenly;
}
 
.feed_header div { width:33%; }
div.feed .item{
	width:100%;
	display:flex;
  border-bottom:1px solid #aaaaaa;
}


ion-card{
	border-radius:2px;
}

ion-card .image{
	width:100%;
	height:130px;
	background-position: center center;
	border-radius:2px;
}

.topnews{
	display:flex;
	justify-content: space-between;
	margin-top:15px;
}

.topnews .title{
	font-size:100%;
  font-weight:700;
  display:flex;
  align-items:center;
}

.topnews .date{
	font-size:100%;
	color:#666768;
	font-weight:700;
}

ion-content, .card-content-ios{
	padding-top:0px;
}
.back{
  position: absolute;
  top: 7px;
  left: 20px;
  width: 0 !important; 
  height: 0; 
  border-top: 10px solid transparent;
  border-bottom: 10px solid transparent; 
  border-right:10px solid blue; 
}



.circleNotification{
  background: #f00f00;
  width: 13px;
  height: 13px;
  border-radius: 100%;
  display: block;
  line-height: 13px;
  text-align: center;
  color: white;
  margin-right: 7px;
}

