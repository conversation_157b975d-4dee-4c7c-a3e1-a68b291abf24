/************/
header{
    width:100%;
    height:250px;
    background: url("../../assets/phenix_splatter.png") no-repeat, #0a465f;
    background-size: cover;
    background-position: center center;
    position: relative;
  
  }
  
  
  .homeIcon{
    position: absolute;
    top: 45px;
    left: 20px;
    width: 30px;
  }
  
  .settingsIcon{
    position: absolute;
    top: 45px;
    right: 20px;
    width: 30px;
  }
  
  .schoolLogo{
    width: 105px;
    height: 105px;
    border-radius: 100%;
    margin: 0 auto;
    display: block;
    position: absolute;
    left:50%;
    top:50%;
    transform: translateY(-50%) translateX(-50%);
    background-color: white;
    padding: 5px;
  }
  
  .schoolText{
    position: absolute;
    bottom:20px;
    width:100%;
    left:50%;
    transform:translateX(-50%);
  }
  
  h2.schoolName{
    text-align: center;
    font-size: 78%;
    color: #fff;
    font-weight: 700;
    padding-top: 24px;
    margin:0;
    text-transform: uppercase;
  }
  
  h2.motto{
    text-align: center;
    font-size: 50%;
    color: #fff;
    margin: 0px;
    padding: 0px;
    padding-top:7px;
  }
  
  .feed_header div .back{
    /*font-size: 200%;
    position: absolute;
    top: 0px;*/
  }
  
  
  /************/
  div.feed_header{
    text-align: center;
    background: #eeeeee;
    color: #0a465f;
    text-transform: uppercase;
    font-weight: 700;
    padding-top: 10px;
    padding-bottom: 10px;
    font-size: 80%;
    display: flex;
    justify-content: space-evenly;
  }
   
.feed_header div { width:33%; font-size:100%; }

.circles{
    display:flex;
    justify-content:space-evenly;
    flex-wrap: wrap;
    padding-top:20px;
}

p.address{
    margin:0 auto;
    width:70%;
    text-align: center;
    font-size:80%;
    font-weight:700;
}

.circles .circle{
    width:70px;
    height:70px;
    border-radius:100%;
    background:grey;
    text-align:center;
    line-height:40px;
}
.circles .circle img{
    margin-top: 23px;
}

p.about{
    font-size:60%;
}

.aboutBox{
  width:80%;
  margin:0 auto;
  //border: 2px solid #eeeeee;
  padding:10px;
}

.back{
  position: absolute;
  top: 7px;
  left: 20px;
  width: 0 !important; 
  height: 0; 
  border-top: 10px solid transparent;
  border-bottom: 10px solid transparent; 
  border-right:10px solid blue; 
}