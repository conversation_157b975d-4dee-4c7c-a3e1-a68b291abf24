import { Component, OnInit } from '@angular/core';
import { NavController, Platform } from '@ionic/angular';
import { GenericService } from '../generic.service';
import * as $ from 'jquery';

import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

import { Storage } from '@ionic/storage';
import { HttpService } from '../service/http.service';
import { ToastService } from '../service/toast.service';

@Component({
  selector: 'app-home',
  templateUrl: 'home.page.html',
  styleUrls: ['home.page.scss'],
})


export class HomePage implements OnInit {

  tiles:any;
  tileKey =  'tile-list';
  appid: any;
  showLoader:any =true
  showMessage:any = false

  constructor(private platform: Platform, 
    private storage: Storage, 
    private generic: GenericService, 
    public navCtrl: NavController, 
    public http: HttpClient, 
    public httpService: HttpService,
    public toaster: ToastService
  ) {
    this.checkNotificationsLoop();
  }

  ngOnInit() {
    this.getAllSchool()
  }

  getAllSchool(){
    this.storage.get('schoolid').then((val) => {
      this.appid = val;
      this.generic.appid = val;
      // this.generic.setup();
 
      this.storage.get(this.tileKey).then((val) => { 
         this.tiles = val;
       });
      
      if(this.tiles == null){
            let req = this.httpService.getAllRecords(this.appid, 'tiles').subscribe((response:any) => {
              if(response.status == 200){
                if(response.data && response.data.length > 0){
                  this.tiles = response.data;
                  this.storage.set(this.tileKey, response.data);
                  this.hardRefresh();
                  this.showLoader = false
                } else {
                  this.showLoader = false
                this.showMessage = true
                }
              } else {
                this.showLoader = false
                this.showMessage = true
              }
          },err=>{
            this.showMessage = true
            this.toaster.showToaster("Something went wrong!", 'danger')
            this.showLoader = false
          });
      }
    });
  }



  doRefresh(event) {
    setTimeout(() => {
      this.hardRefresh();
      event.target.complete();
    }, 2000);
  }


  hardRefresh(){
    this.getAllSchool()
  }

  public checkNotificationsLoop() {
    var me =this;
    setTimeout(function(){
     // me.unreadEventsCount++;
      //me.generic.setup();
      //me.generic.unreadNewslettersCount++;
      me.checkNotificationsLoop();
    },2500);
  }

}
