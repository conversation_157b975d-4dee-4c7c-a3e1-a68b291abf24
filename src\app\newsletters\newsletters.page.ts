import { Component, OnInit } from '@angular/core';
import { GenericService } from '../generic.service';

import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { Storage } from '@ionic/storage';
import { ToastService } from '../service/toast.service';
import { HttpService } from '../service/http.service';

@Component({
  selector: 'app-newsletters',
  templateUrl: './newsletters.page.html',
  styleUrls: ['./newsletters.page.scss'],
})
export class NewslettersPage implements OnInit {

  newsletters: any;
  showLoader:any =true
  showMessage:any = false

  constructor(private storage: Storage, 
    public generic: GenericService, 
    private httpService: HttpService, 
    public toaster: ToastService)
  {
    // generic.setup();
  }

  ngOnInit() {
    this.getAllNewLetters()
  }

  getAllNewLetters(){
    this.storage.get('schoolid').then((val) => {
      this.httpService.getAllRecords(val, 'newsletters').subscribe((response:any) => {
        if(response.status == 200){
          if(response.data && response.data.length > 0){
            this.newsletters = response.data;
            this.showLoader = false
          } else {
            this.showMessage = true
            this.showLoader = false
          }
        } else {
          this.showLoader = false
          this.showMessage = true
        }
      }, err=>{
        this.showMessage = true
        this.toaster.showToaster("Something went wrong!", 'danger')
        this.showLoader = false
      });
    });
  }

}
