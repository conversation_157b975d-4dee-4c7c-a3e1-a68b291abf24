import { NgModule } from '@angular/core';
import { PreloadAllModules, RouterModule, Routes } from '@angular/router';

const routes: Routes = [
  { path: '', redirectTo: 'search', pathMatch: 'full' },
  { path: 'home', loadChildren: './home/<USER>' },
  { path: 'event-list', loadChildren: './event-list/event-list.module#EventListPageModule' },
  { path: 'event-detail/:id', loadChildren: './event-detail/event-detail.module#EventDetailPageModule' },
  { path: 'search', loadChildren: './search/search.module#SearchPageModule' },
  { path: 'news', loadChildren: './news/news.module#NewsPageModule' },
  { path: 'news-detail/:id', loadChildren: './news-detail/news-detail.module#NewsDetailPageModule' },
  { path: 'newsletters', loadChildren: './newsletters/newsletters.module#NewslettersPageModule' },
  { path: 'gallery', loadChildren: './gallery/gallery.module#GalleryPageModule' },
  { path: 'contact', loadChildren: './contact/contact.module#ContactPageModule' },
  { path: 'settings', loadChildren: './settings/settings.module#SettingsPageModule' },
  { path: 'gallery-detail', loadChildren: './gallery-detail/gallery-detail.module#GalleryDetailPageModule' },
  { path: 'letters', loadChildren: './letters/letters.module#LettersPageModule' },
];

@NgModule({
  imports: [
    RouterModule.forRoot(routes, { preloadingStrategy: PreloadAllModules })
  ],
  exports: [RouterModule]
})
export class AppRoutingModule { }
