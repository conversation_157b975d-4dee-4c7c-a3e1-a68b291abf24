import { Component, OnInit } from '@angular/core';
import * as $ from 'jquery';
import { GenericService } from '../generic.service';

import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { Storage } from '@ionic/storage';
import { NavController } from '@ionic/angular';
import { environment } from 'src/environments/environment';



@Component({
  selector: 'app-search',
  templateUrl: './search.page.html',
  styleUrls: ['./search.page.scss'],
})

 

export class SearchPage implements OnInit {

  searchvalue: any;
  results: any;
  show=false
  constructor(private navCtrl: NavController, public generic: GenericService, private storage: Storage, private http: HttpClient) { 

    storage.get('schoolid').then((val) => {
      console.log(val);
      if(Number(val)){
        this.generic.appid = val;
        generic.setup();
        generic.loadTile('home','#');
        
        this.navCtrl.navigateRoot('/home');

      }
    });

  }

  ngOnInit() {
  }

  setSchool(id){

    this.http.get(`${environment.baseUrl}/?dev=1&key=trickydicky&feed=installed&school_id=` + id).subscribe((response) => {
      this.results = response['data'];
    });

    this.storage.set('schoolid', id);

    console.log('Setting school id: ' + id);
    this.generic.appid = id;
    this.generic.loadTile('settings','#');
  }

  search(event){
    this.show = false

    if(this.searchvalue.length >= 4){
      console.log("true");
     this.show = true

      this.http.get(`${environment.baseUrl}/?dev=1&key=trickydicky&feed=search&q=` + this.searchvalue).subscribe((response) => {
        this.results = response['data'];
      });
    } else {
     this.show = false
    }


    // if(this.searchvalue != '' ){
    //  this.show = true
    // }else{
    //  this.show = false
    // }

   if(event.key === 1111|| event.keyCode === 13) {
      console.log('asdf');
    }


    return true
}


}
