<app-header></app-header>

<ion-content class=" ion-content-height">

	<div class="feed_header">
		<div (click)="generic.loadTile('news','#')">
			<div class="back" [ngStyle]="{ 'border-right' : '10px solid ' + generic.secondColor }"></div>
		</div>
		<div>News</div>
		<div>&nbsp;</div>
	</div>

	<div class="feed">
		<ion-card>
			<ion-card-header>
				<div class="topnews">
					<div class="title" [ngStyle]="{ 'color' : generic.firstColor }">{{ newsinfo?.name }} </div>
					<div class="date"> {{ newsinfo?.date }}</div>
				</div>
			</ion-card-header>

			<ion-card-content>
				<span [innerHTML]="newsinfo?.description"></span>
				<Br />
				<div class="image" *ngIf="newsinfo?.dfile"
					[ngStyle]="{'background-image': 'url(' + generic.appinfo?.url + 'images/image_gallery/large/'+ newsinfo?.dfile + ')'}">
				</div>
			</ion-card-content>
			<div class="no-data-style" *ngIf="showMessage">
				<p>No data found!</p>
			  </div>
			</ion-card>
			
		</div>
		<div *ngIf="showLoader" class="loader-style">
		  <ion-spinner name="crescent" style="color: #158145;"></ion-spinner>
		</div>
</ion-content>